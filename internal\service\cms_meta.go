package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type CmsMetaService interface {
	Create(ctx context.Context, req *v1.CmsMetaCreateParams) error
	Update(ctx context.Context, id uint, req *v1.CmsMetaUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.CmsMetaResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewCmsMetaService(
	service *Service,
	cmsMetaRepository repository.CmsMetaRepository,
) CmsMetaService {
	return &cmsMetaService{
		Service:           service,
		cmsMetaRepository: cmsMetaRepository,
	}
}

type cmsMetaService struct {
	*Service
	cmsMetaRepository repository.CmsMetaRepository
}

// 栏目相关方法实现
func (s *cmsMetaService) Create(ctx context.Context, req *v1.CmsMetaCreateParams) error {
	meta := &model.CmsMeta{
		Name:     req.Name,
		Slug:     req.Slug,
		Cover:    req.Cover,
		Summary:  req.Summary,
		Order:    req.Order,
		Status:   req.Status,
		ParentId: req.ParentId,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsMetaRepository.Create(ctx, meta); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsMetaService) Update(ctx context.Context, id uint, req *v1.CmsMetaUpdateParams) error {
	meta, err := s.cmsMetaRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Name != "" {
		meta.Name = req.Name
	}
	if req.Slug != "" {
		meta.Slug = req.Slug
	}
	meta.Cover = req.Cover
	if req.Summary != "" {
		meta.Summary = req.Summary
	}
	meta.Order = req.Order
	if req.Status {
		meta.Status = req.Status
	}
	meta.ParentId = req.ParentId

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsMetaRepository.Update(ctx, meta); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsMetaService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsMetaRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsMetaService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsMetaRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsMetaService) Get(ctx context.Context, id uint) (*v1.CmsMetaResponse, error) {
	meta, err := s.cmsMetaRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.CmsMetaResponse{
		ID:        meta.ID,
		Name:      meta.Name,
		Slug:      meta.Slug,
		Cover:     meta.Cover,
		Summary:   meta.Summary,
		Order:     meta.Order,
		Status:    meta.Status,
		ParentId:  meta.ParentId,
		CreatedAt: meta.CreatedAt.Format(time.RFC3339),
		UpdatedAt: meta.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *cmsMetaService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 别名筛选
	slug := ctx.(*gin.Context).DefaultQuery("slug", "")
	if slug != "" {
		params.AddFilter("slug_like", slug)
	}

	metas, total, err := s.cmsMetaRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.CmsMetaResponse, 0)
	for _, meta := range metas {
		records = append(records, &v1.CmsMetaResponse{
			ID:        meta.ID,
			Name:      meta.Name,
			Slug:      meta.Slug,
			Cover:     meta.Cover,
			Summary:   meta.Summary,
			Order:     meta.Order,
			Status:    meta.Status,
			ParentId:  meta.ParentId,
			CreatedAt: meta.CreatedAt.Format(time.RFC3339),
			UpdatedAt: meta.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
