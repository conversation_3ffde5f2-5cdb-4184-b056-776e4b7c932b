package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type BusinessProductService interface {
	Create(ctx context.Context, req *v1.BusinessProductCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessProductUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessProductResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessProductService(
	service *Service,
	businessProductRepository repository.BusinessProductRepository,
	businessCompanyRepository repository.BusinessCompanyRepository,
) BusinessProductService {
	return &businessProductService{
		Service:                   service,
		businessProductRepository: businessProductRepository,
		businessCompanyRepository: businessCompanyRepository,
	}
}

type businessProductService struct {
	*Service
	businessProductRepository repository.BusinessProductRepository
	businessCompanyRepository repository.BusinessCompanyRepository
}

// 产品相关方法实现
func (s *businessProductService) Create(ctx context.Context, req *v1.BusinessProductCreateParams) error {
	product := &model.BusinessProduct{
		CompanyId: req.CompanyId,
		Name:      req.Name,
		Code:      req.Code,
		Type:      req.Type,
		Spec:      req.Spec,
		Material:  req.Material,
		Standard:  req.Standard,
		Surface:   req.Surface,
		Strength:  req.Strength,
		Cover:     req.Cover,
		Detail:    req.Detail,
		Albums:    req.Albums,
		Tags:      req.Tags,
		Order:     req.Order,
		Flag:      req.Flag,
		Status:    req.Status,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessProductRepository.Create(ctx, product); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessProductService) Update(ctx context.Context, id uint, req *v1.BusinessProductUpdateParams) error {
	product, err := s.businessProductRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.CompanyId > 0 {
		product.CompanyId = req.CompanyId
	}
	if req.Name != "" {
		product.Name = req.Name
	}
	if req.Code != "" {
		product.Code = req.Code
	}
	if req.Type > 0 {
		product.Type = req.Type
	}
	if req.Spec != "" {
		product.Spec = req.Spec
	}
	if req.Material != "" {
		product.Material = req.Material
	}
	if req.Standard != "" {
		product.Standard = req.Standard
	}
	if req.Surface != "" {
		product.Surface = req.Surface
	}
	if req.Strength != "" {
		product.Strength = req.Strength
	}
	if req.Cover != "" {
		product.Cover = req.Cover
	}
	if req.Detail != "" {
		product.Detail = req.Detail
	}
	if req.Albums != nil {
		product.Albums = req.Albums
	}
	if req.Tags != nil {
		product.Tags = req.Tags
	}
	product.Order = req.Order
	if req.Flag != nil {
		product.Flag = req.Flag
	}
	product.Status = req.Status

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessProductRepository.Update(ctx, product); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessProductService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessProductRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessProductService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessProductRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessProductService) Get(ctx context.Context, id uint) (*v1.BusinessProductResponse, error) {
	product, err := s.businessProductRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.BusinessProductResponse{
		ID:        product.ID,
		CompanyId: product.CompanyId,
		Name:      product.Name,
		Code:      product.Code,
		Type:      product.Type,
		Spec:      product.Spec,
		Material:  product.Material,
		Standard:  product.Standard,
		Surface:   product.Surface,
		Strength:  product.Strength,
		Cover:     product.Cover,
		Detail:    product.Detail,
		Albums:    product.Albums,
		Tags:      product.Tags,
		Order:     product.Order,
		Flag:      product.Flag,
		Status:    product.Status,
		CreatedAt: product.CreatedAt.Format(time.RFC3339),
		UpdatedAt: product.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *businessProductService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 编码筛选
	code := ctx.(*gin.Context).DefaultQuery("code", "")
	if code != "" {
		params.AddFilter("code_like", code)
	}

	// 公司筛选
	companyId := ctx.(*gin.Context).DefaultQuery("companyId", "")
	if companyId != "" {
		params.AddFilter("company_id", companyId)
	}

	// 类型筛选
	productType := ctx.(*gin.Context).DefaultQuery("type", "")
	if productType != "" {
		params.AddFilter("type", productType)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	products, total, err := s.businessProductRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 检查是否需要展开公司信息
	expandCompany := ctx.(*gin.Context).Query("_expand") == "company"

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessProductResponse, 0)
	for _, product := range products {
		productResponse := &v1.BusinessProductResponse{
			ID:        product.ID,
			CompanyId: product.CompanyId,
			Name:      product.Name,
			Code:      product.Code,
			Type:      product.Type,
			Spec:      product.Spec,
			Material:  product.Material,
			Standard:  product.Standard,
			Surface:   product.Surface,
			Strength:  product.Strength,
			Cover:     product.Cover,
			Detail:    product.Detail,
			Albums:    product.Albums,
			Tags:      product.Tags,
			Order:     product.Order,
			Flag:      product.Flag,
			Status:    product.Status,
			CreatedAt: product.CreatedAt.Format(time.RFC3339),
			UpdatedAt: product.UpdatedAt.Format(time.RFC3339),
		}

		// 如果需要展开公司信息，则获取公司详情
		if expandCompany && product.CompanyId > 0 {
			company, err := s.businessCompanyRepository.Get(ctx, product.CompanyId)
			if err == nil {
				productResponse.Company = &v1.BusinessCompanyResponse{
					ID:        company.ID,
					Name:      company.Name,
					Industry:  company.Industry,
					Type:      company.Type,
					Size:      company.Size,
					Cert:      company.Cert,
					Contact:   company.Contact,
					Area:      company.Area,
					Address:   company.Address,
					Phone:     company.Phone,
					Email:     company.Email,
					Website:   company.Website,
					Logo:      company.Logo,
					Summary:   company.Summary,
					Detail:    company.Detail,
					Albums:    company.Albums,
					Tags:      company.Tags,
					Order:     company.Order,
					Flag:      company.Flag,
					Status:    company.Status,
					CreatedAt: company.CreatedAt.Format(time.RFC3339),
					UpdatedAt: company.UpdatedAt.Format(time.RFC3339),
				}
			}
		}

		records = append(records, productResponse)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
