package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type BusinessExpertService interface {
	Create(ctx context.Context, req *v1.BusinessExpertCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessExpertUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessExpertResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessExpertService(
	service *Service,
	businessExpertRepository repository.BusinessExpertRepository,
) BusinessExpertService {
	return &businessExpertService{
		Service:                  service,
		businessExpertRepository: businessExpertRepository,
	}
}

type businessExpertService struct {
	*Service
	businessExpertRepository repository.BusinessExpertRepository
}

// 专家相关方法实现
func (s *businessExpertService) Create(ctx context.Context, req *v1.BusinessExpertCreateParams) error {
	expert := &model.BusinessExpert{
		Name:   req.Name,
		Gender: req.Gender,
		Age:    req.Age,
		Edu:    req.Edu,
		Exp:    req.Exp,
		Type:   req.Type,
		Skills: req.Skills,
		Area:   req.Area,
		Phone:  req.Phone,
		Email:  req.Email,
		Avatar: req.Avatar,
		Tags:    req.Tags,
		Summary: req.Summary,
		Detail:  req.Detail,
		Party:  req.Party,
		Order:  req.Order,
		Flag:   req.Flag,
		Status: req.Status,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessExpertRepository.Create(ctx, expert); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessExpertService) Update(ctx context.Context, id uint, req *v1.BusinessExpertUpdateParams) error {
	expert, err := s.businessExpertRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Name != "" {
		expert.Name = req.Name
	}
	expert.Gender = req.Gender
	expert.Age = req.Age
	expert.Edu = req.Edu
	expert.Exp = req.Exp
	if req.Type != nil {
		expert.Type = req.Type
	}
	if req.Skills != nil {
		expert.Skills = req.Skills
	}
	if req.Area != "" {
		expert.Area = req.Area
	}
	if req.Phone != "" {
		expert.Phone = req.Phone
	}
	if req.Email != "" {
		expert.Email = req.Email
	}
	if req.Avatar != "" {
		expert.Avatar = req.Avatar
	}
	if req.Tags != nil {
		expert.Tags = req.Tags
	}
	if req.Summary != "" {
		expert.Summary = req.Summary
	}
	if req.Detail != "" {
		expert.Detail = req.Detail
	}
	expert.Party = req.Party
	expert.Order = req.Order
	if req.Flag != nil {
		expert.Flag = req.Flag
	}
	expert.Status = req.Status

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessExpertRepository.Update(ctx, expert); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessExpertService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessExpertRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessExpertService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessExpertRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessExpertService) Get(ctx context.Context, id uint) (*v1.BusinessExpertResponse, error) {
	expert, err := s.businessExpertRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.BusinessExpertResponse{
		ID:        expert.ID,
		Name:      expert.Name,
		Gender:    expert.Gender,
		Age:       expert.Age,
		Edu:       expert.Edu,
		Exp:       expert.Exp,
		Type:      expert.Type,
		Skills:    expert.Skills,
		Area:      expert.Area,
		Phone:     expert.Phone,
		Email:     expert.Email,
		Avatar:    expert.Avatar,
		Tags:      expert.Tags,
		Summary:   expert.Summary,
		Detail:    expert.Detail,
		Party:     expert.Party,
		Order:     expert.Order,
		Flag:      expert.Flag,
		Status:    expert.Status,
		CreatedAt: expert.CreatedAt.Format(time.RFC3339),
		UpdatedAt: expert.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *businessExpertService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 性别筛选
	gender := ctx.(*gin.Context).DefaultQuery("gender", "")
	if gender != "" {
		params.AddFilter("gender", gender)
	}

	// 地区筛选
	area := ctx.(*gin.Context).DefaultQuery("area", "")
	if area != "" {
		params.AddFilter("area_like", area)
	}

	// 党员筛选
	party := ctx.(*gin.Context).DefaultQuery("party", "")
	if party != "" {
		params.AddFilter("party", party)
	}

	// 手机筛选
	phone := ctx.(*gin.Context).DefaultQuery("phone", "")
	if phone != "" {
		params.AddFilter("phone", phone)
	}

	// 邮箱筛选
	email := ctx.(*gin.Context).DefaultQuery("email", "")
	if email != "" {
		params.AddFilter("email", email)
	}

	// 擅长领域筛选
	typeValue := ctx.(*gin.Context).DefaultQuery("type", "")
	if typeValue != "" {
		params.AddFilter("type_any", typeValue)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	experts, total, err := s.businessExpertRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessExpertResponse, 0, len(experts))
	for _, expert := range experts {
		records = append(records, &v1.BusinessExpertResponse{
			ID:        expert.ID,
			Name:      expert.Name,
			Gender:    expert.Gender,
			Age:       expert.Age,
			Edu:       expert.Edu,
			Exp:       expert.Exp,
			Type:      expert.Type,
			Skills:    expert.Skills,
			Area:      expert.Area,
			Phone:     expert.Phone,
			Email:     expert.Email,
			Avatar:    expert.Avatar,
			Tags:      expert.Tags,
			Summary:   expert.Summary,
			Detail:    expert.Detail,
			Party:     expert.Party,
			Order:     expert.Order,
			Flag:      expert.Flag,
			Status:    expert.Status,
			CreatedAt: expert.CreatedAt.Format(time.RFC3339),
			UpdatedAt: expert.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
