package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type BusinessReportService interface {
	Create(ctx context.Context, req *v1.BusinessReportCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessReportUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessReportResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessReportService(
	service *Service,
	businessReportRepository repository.BusinessReportRepository,
	businessCompanyRepository repository.BusinessCompanyRepository,
) BusinessReportService {
	return &businessReportService{
		Service:                   service,
		businessReportRepository:  businessReportRepository,
		businessCompanyRepository: businessCompanyRepository,
	}
}

type businessReportService struct {
	*Service
	businessReportRepository  repository.BusinessReportRepository
	businessCompanyRepository repository.BusinessCompanyRepository
}

// 报告相关方法实现
func (s *businessReportService) Create(ctx context.Context, req *v1.BusinessReportCreateParams) error {
	report := &model.BusinessReport{
		CompanyId: req.CompanyId,
		Title:     req.Title,
		Summary:   req.Summary,
		Content:   req.Content,
		Tags:      req.Tags,
		Cover:     req.Cover,
		Files:     req.Files,
		Order:     req.Order,
		Flag:      req.Flag,
		Status:    req.Status,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessReportRepository.Create(ctx, report); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessReportService) Update(ctx context.Context, id uint, req *v1.BusinessReportUpdateParams) error {
	report, err := s.businessReportRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.CompanyId > 0 {
		report.CompanyId = req.CompanyId
	}
	if req.Title != "" {
		report.Title = req.Title
	}
	if req.Summary != "" {
		report.Summary = req.Summary
	}
	if req.Content != "" {
		report.Content = req.Content
	}
	report.Cover = req.Cover
	if req.Tags != nil {
		report.Tags = req.Tags
	}
	if req.Files != nil {
		report.Files = req.Files
	}
	report.Order = req.Order
	if req.Flag != nil {
		report.Flag = req.Flag
	}
	report.Status = req.Status

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessReportRepository.Update(ctx, report); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessReportService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessReportRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessReportService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessReportRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessReportService) Get(ctx context.Context, id uint) (*v1.BusinessReportResponse, error) {
	report, err := s.businessReportRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.BusinessReportResponse{
		ID:        report.ID,
		CompanyId: report.CompanyId,
		Title:     report.Title,
		Summary:   report.Summary,
		Content:   report.Content,
		Tags:      report.Tags,
		Cover:     report.Cover,
		Files:     report.Files,
		Order:     report.Order,
		Flag:      report.Flag,
		Status:    report.Status,
		CreatedAt: report.CreatedAt.Format(time.RFC3339),
		UpdatedAt: report.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *businessReportService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 标题筛选
	title := ctx.(*gin.Context).DefaultQuery("title", "")
	if title != "" {
		params.AddFilter("title_like", title)
	}

	// 公司ID筛选
	companyId := ctx.(*gin.Context).DefaultQuery("companyId", "")
	if companyId != "" {
		params.AddFilter("company_id", companyId)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	reports, total, err := s.businessReportRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 检查是否需要展开公司信息
	expandCompany := ctx.(*gin.Context).Query("_expand") == "company"

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessReportResponse, 0)
	for _, report := range reports {
		reportResponse := &v1.BusinessReportResponse{
			ID:        report.ID,
			CompanyId: report.CompanyId,
			Title:     report.Title,
			Summary:   report.Summary,
			Content:   report.Content,
			Tags:      report.Tags,
			Cover:     report.Cover,
			Files:     report.Files,
			Order:     report.Order,
			Flag:      report.Flag,
			Status:    report.Status,
			CreatedAt: report.CreatedAt.Format(time.RFC3339),
			UpdatedAt: report.UpdatedAt.Format(time.RFC3339),
		}

		// 如果需要展开公司信息，则获取公司详情
		if expandCompany && report.CompanyId > 0 {
			company, err := s.businessCompanyRepository.Get(ctx, report.CompanyId)
			if err == nil {
				reportResponse.Company = &v1.BusinessCompanyResponse{
					ID:        company.ID,
					Name:      company.Name,
					Industry:  company.Industry,
					Type:      company.Type,
					Size:      company.Size,
					Cert:      company.Cert,
					Contact:   company.Contact,
					Area:      company.Area,
					Address:   company.Address,
					Phone:     company.Phone,
					Email:     company.Email,
					Website:   company.Website,
					Logo:      company.Logo,
					Summary:   company.Summary,
					Detail:    company.Detail,
					Albums:    company.Albums,
					Tags:      company.Tags,
					Order:     company.Order,
					Flag:      company.Flag,
					Status:    company.Status,
					CreatedAt: company.CreatedAt.Format(time.RFC3339),
					UpdatedAt: company.UpdatedAt.Format(time.RFC3339),
				}
			}
		}

		records = append(records, reportResponse)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
