package model

import (
	v1 "daisy-server/api/v1"

	"github.com/lib/pq"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type BusinessCompany struct {
	gorm.Model
	Name     string                `gorm:"size:255; not null; unique; comment:企业名称"`
	Industry uint                  `gorm:"default:0; index; comment:所属行业"`
	Type     uint                  `gorm:"default:0; index; comment:企业类型"`
	Size     uint                  `gorm:"default:0; index; comment:企业规模"`
	Cert     pq.Int64Array         `gorm:"type:integer[]; comment:资质"`
	Contact  string                `gorm:"size:64; comment:联系人"`
	Area     string                `gorm:"size:32; comment:地区"`
	Address  string                `gorm:"size:255; comment:地址"`
	Phone    string                `gorm:"size:20; comment:电话"`
	Email    string                `gorm:"size:64; comment:邮箱"`
	Website  string                `gorm:"size:64; comment:网站"`
	Logo     string                `gorm:"size:255; comment:logo"`
	Summary  string                `gorm:"type:text; comment:企业简介"`
	Detail   string                `gorm:"type:text; comment:详情"`
	Albums   []v1.UploadFileParams `gorm:"type:jsonb; serializer:json; comment:企业相册"`
	Tags     datatypes.JSON        `gorm:"type:jsonb; comment:标签"`
	Order    int                   `gorm:"default:0; index; comment:排序"`
	Flag     pq.Int64Array         `gorm:"type:integer[]; comment:标志"`
	Status   bool                  `gorm:"default:false; index; comment:状态"`
}

func (m *BusinessCompany) TableName() string {
	return "business_company"
}
