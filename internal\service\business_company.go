package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type BusinessCompanyService interface {
	Create(ctx context.Context, req *v1.BusinessCompanyCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessCompanyUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessCompanyResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessCompanyService(
	service *Service,
	businessCompanyRepository repository.BusinessCompanyRepository,
) BusinessCompanyService {
	return &businessCompanyService{
		Service:                   service,
		businessCompanyRepository: businessCompanyRepository,
	}
}

type businessCompanyService struct {
	*Service
	businessCompanyRepository repository.BusinessCompanyRepository
}

// 企业相关方法实现
func (s *businessCompanyService) Create(ctx context.Context, req *v1.BusinessCompanyCreateParams) error {
	company := &model.BusinessCompany{
		Name:     req.Name,
		Industry: req.Industry,
		Type:     req.Type,
		Size:     req.Size,
		Cert:     req.Cert,
		Contact:  req.Contact,
		Area:     req.Area,
		Address:  req.Address,
		Phone:    req.Phone,
		Email:    req.Email,
		Website:  req.Website,
		Logo:     req.Logo,
		Summary:  req.Summary,
		Detail:   req.Detail,
		Albums:   req.Albums,
		Tags:     req.Tags,
		Order:    req.Order,
		Flag:     req.Flag,
		Status:   req.Status,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessCompanyRepository.Create(ctx, company); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessCompanyService) Update(ctx context.Context, id uint, req *v1.BusinessCompanyUpdateParams) error {
	company, err := s.businessCompanyRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Name != "" {
		company.Name = req.Name
	}
	if req.Industry > 0 {
		company.Industry = req.Industry
	}
	if req.Type > 0 {
		company.Type = req.Type
	}
	if req.Size > 0 {
		company.Size = req.Size
	}
	if req.Cert != nil {
		company.Cert = req.Cert
	}
	if req.Contact != "" {
		company.Contact = req.Contact
	}
	if req.Area != "" {
		company.Area = req.Area
	}
	if req.Address != "" {
		company.Address = req.Address
	}
	if req.Phone != "" {
		company.Phone = req.Phone
	}
	if req.Email != "" {
		company.Email = req.Email
	}
	if req.Website != "" {
		company.Website = req.Website
	}
	company.Logo = req.Logo
	if req.Summary != "" {
		company.Summary = req.Summary
	}
	if req.Detail != "" {
		company.Detail = req.Detail
	}
	if req.Albums != nil {
		company.Albums = req.Albums
	}
	if req.Tags != nil {
		company.Tags = req.Tags
	}
	company.Order = req.Order
	if req.Flag != nil {
		company.Flag = req.Flag
	}
	company.Status = req.Status

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessCompanyRepository.Update(ctx, company); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessCompanyService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessCompanyRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessCompanyService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessCompanyRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessCompanyService) Get(ctx context.Context, id uint) (*v1.BusinessCompanyResponse, error) {
	company, err := s.businessCompanyRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.BusinessCompanyResponse{
		ID:        company.ID,
		Name:      company.Name,
		Industry:  company.Industry,
		Type:      company.Type,
		Size:      company.Size,
		Cert:      company.Cert,
		Contact:   company.Contact,
		Area:      company.Area,
		Address:   company.Address,
		Phone:     company.Phone,
		Email:     company.Email,
		Website:   company.Website,
		Logo:      company.Logo,
		Summary:   company.Summary,
		Detail:    company.Detail,
		Albums:    company.Albums,
		Tags:      company.Tags,
		Order:     company.Order,
		Flag:      company.Flag,
		Status:    company.Status,
		CreatedAt: company.CreatedAt.Format(time.RFC3339),
		UpdatedAt: company.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *businessCompanyService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 行业筛选
	industry := ctx.(*gin.Context).DefaultQuery("industry", "")
	if industry != "" {
		params.AddFilter("industry", industry)
	}

	// 类型筛选
	companyType := ctx.(*gin.Context).DefaultQuery("type", "")
	if companyType != "" {
		params.AddFilter("type", companyType)
	}

	// 规模筛选
	size := ctx.(*gin.Context).DefaultQuery("size", "")
	if size != "" {
		params.AddFilter("size", size)
	}

	// 资质筛选
	cert := ctx.(*gin.Context).DefaultQuery("cert", "")
	if cert != "" {
		params.AddFilter("cert_any", cert)
	}

	// 地区筛选
	area := ctx.(*gin.Context).DefaultQuery("area", "")
	if area != "" {
		params.AddFilter("area_like", area)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	companies, total, err := s.businessCompanyRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessCompanyResponse, 0)
	for _, company := range companies {
		records = append(records, &v1.BusinessCompanyResponse{
			ID:        company.ID,
			Name:      company.Name,
			Industry:  company.Industry,
			Type:      company.Type,
			Size:      company.Size,
			Cert:      company.Cert,
			Contact:   company.Contact,
			Area:      company.Area,
			Address:   company.Address,
			Phone:     company.Phone,
			Email:     company.Email,
			Website:   company.Website,
			Logo:      company.Logo,
			Summary:   company.Summary,
			Detail:    company.Detail,
			Albums:    company.Albums,
			Tags:      company.Tags,
			Order:     company.Order,
			Flag:      company.Flag,
			Status:    company.Status,
			CreatedAt: company.CreatedAt.Format(time.RFC3339),
			UpdatedAt: company.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
