package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type SysRoleService interface {
	Create(ctx context.Context, req *v1.SysRoleCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysRoleUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysRoleResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysRoleService(
	service *Service,
	sysRoleRepository repository.SysRoleRepository,
) SysRoleService {
	return &sysRoleService{
		Service:           service,
		sysRoleRepository: sysRoleRepository,
	}
}

type sysRoleService struct {
	*Service
	sysRoleRepository repository.SysRoleRepository
}

// 角色相关方法实现
func (s *sysRoleService) Create(ctx context.Context, req *v1.SysRoleCreateParams) error {
	role := &model.SysRole{
		Name:    req.Name,
		Code:    req.Code,
		Summary: req.Summary,
		Status:  req.Status,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysRoleRepository.Create(ctx, role); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysRoleService) Update(ctx context.Context, id uint, req *v1.SysRoleUpdateParams) error {
	role, err := s.sysRoleRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Name != "" {
		role.Name = req.Name
	}
	if req.Code != "" {
		role.Code = req.Code
	}
	if req.Summary != "" {
		role.Summary = req.Summary
	}
	if req.Status {
		role.Status = req.Status
	}
	if req.Home != "" {
		role.Home = req.Home
	}
	if req.MenuIds != nil {
		role.MenuIds = req.MenuIds
	}
	if req.ApiIds != nil {
		role.ApiIds = req.ApiIds
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysRoleRepository.Update(ctx, role); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysRoleService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysRoleRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysRoleService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysRoleRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysRoleService) Get(ctx context.Context, id uint) (*v1.SysRoleResponse, error) {
	role, err := s.sysRoleRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.SysRoleResponse{
		ID:        role.ID,
		Name:      role.Name,
		Code:      role.Code,
		Summary:   role.Summary,
		Status:    role.Status,
		Home:      role.Home,
		MenuIds:   role.MenuIds,
		ApiIds:    role.ApiIds,
		CreatedAt: role.CreatedAt.Format(time.RFC3339),
		UpdatedAt: role.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *sysRoleService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 编码筛选
	code := ctx.(*gin.Context).DefaultQuery("code", "")
	if code != "" {
		params.AddFilter("code", code)
	}

	roles, total, err := s.sysRoleRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysRoleResponse, 0)
	for _, role := range roles {
		records = append(records, &v1.SysRoleResponse{
			ID:        role.ID,
			Name:      role.Name,
			Code:      role.Code,
			Summary:   role.Summary,
			Status:    role.Status,
			Home:      role.Home,
			MenuIds:   role.MenuIds,
			ApiIds:    role.ApiIds,
			CreatedAt: role.CreatedAt.Format(time.RFC3339),
			UpdatedAt: role.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
