package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type CmsPostService interface {
	Create(ctx context.Context, req *v1.CmsPostCreateParams) error
	Update(ctx context.Context, id uint, req *v1.CmsPostUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.CmsPostResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewCmsPostService(
	service *Service,
	cmsPostRepository repository.CmsPostRepository,
) CmsPostService {
	return &cmsPostService{
		Service:           service,
		cmsPostRepository: cmsPostRepository,
	}
}

type cmsPostService struct {
	*Service
	cmsPostRepository repository.CmsPostRepository
}

// 文章相关方法实现
func (s *cmsPostService) Create(ctx context.Context, req *v1.CmsPostCreateParams) error {
	post := &model.CmsPost{
		Title:    req.Title,
		Slug:     req.Slug,
		Summary:  req.Summary,
		Content:  req.Content,
		Cover:    req.Cover,
		Author:   req.Author,
		From:     req.From,
		Password: req.Password,
		Tags:     req.Tags,
		Files:    req.Files,
		Order:    req.Order,
		Flag:     req.Flag,
		Status:   req.Status,
		MetaId:   req.MetaId,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsPostRepository.Create(ctx, post); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsPostService) Update(ctx context.Context, id uint, req *v1.CmsPostUpdateParams) error {
	post, err := s.cmsPostRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Title != "" {
		post.Title = req.Title
	}
	if req.Slug != "" {
		post.Slug = req.Slug
	}
	if req.Summary != "" {
		post.Summary = req.Summary
	}
	if req.Content != "" {
		post.Content = req.Content
	}
	post.Cover = req.Cover
	if req.Author != "" {
		post.Author = req.Author
	}
	if req.From != "" {
		post.From = req.From
	}
	if req.Password != "" {
		post.Password = req.Password
	}
	if req.Tags != nil {
		post.Tags = req.Tags
	}
	if req.Files != nil {
		post.Files = req.Files
	}
	post.Order = req.Order
	if req.Flag != nil {
		post.Flag = req.Flag
	}
	if req.Status {
		post.Status = req.Status
	}
	post.MetaId = req.MetaId

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsPostRepository.Update(ctx, post); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsPostService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsPostRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsPostService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.cmsPostRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *cmsPostService) Get(ctx context.Context, id uint) (*v1.CmsPostResponse, error) {
	post, err := s.cmsPostRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.CmsPostResponse{
		ID:        post.ID,
		Title:     post.Title,
		Slug:      post.Slug,
		Summary:   post.Summary,
		Content:   post.Content,
		Cover:     post.Cover,
		Author:    post.Author,
		From:      post.From,
		Password:  post.Password,
		Tags:      post.Tags,
		Files:     post.Files,
		Order:     post.Order,
		Flag:      post.Flag,
		Status:    post.Status,
		MetaId:    post.MetaId,
		CreatedAt: post.CreatedAt.Format(time.RFC3339),
		UpdatedAt: post.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *cmsPostService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 标题筛选
	title := ctx.(*gin.Context).DefaultQuery("title", "")
	if title != "" {
		params.AddFilter("title_like", title)
	}

	// 别名筛选
	slug := ctx.(*gin.Context).DefaultQuery("slug", "")
	if slug != "" {
		params.AddFilter("slug_like", slug)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	// 栏目筛选
	metaId := ctx.(*gin.Context).DefaultQuery("metaId", "")
	if metaId != "" {
		params.AddFilter("meta_id", metaId)
	}

	posts, total, err := s.cmsPostRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.CmsPostResponse, 0)
	for _, post := range posts {
		records = append(records, &v1.CmsPostResponse{
			ID:        post.ID,
			Title:     post.Title,
			Slug:      post.Slug,
			Summary:   post.Summary,
			Content:   post.Content,
			Cover:     post.Cover,
			Author:    post.Author,
			From:      post.From,
			Password:  post.Password,
			Tags:      post.Tags,
			Files:     post.Files,
			Order:     post.Order,
			Flag:      post.Flag,
			Status:    post.Status,
			MetaId:    post.MetaId,
			CreatedAt: post.CreatedAt.Format(time.RFC3339),
			UpdatedAt: post.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
