package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
)

type SysDeptService interface {
	Create(ctx context.Context, req *v1.SysDeptCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysDeptUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysDeptResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysDeptService(
	service *Service,
	sysDeptRepository repository.SysDeptRepository,
) SysDeptService {
	return &sysDeptService{
		Service:           service,
		sysDeptRepository: sysDeptRepository,
	}
}

type sysDeptService struct {
	*Service
	sysDeptRepository repository.SysDeptRepository
}

// 部门相关方法实现
func (s *sysDeptService) Create(ctx context.Context, req *v1.SysDeptCreateParams) error {
	dept := &model.SysDept{
		Name:     req.Name,
		Code:     req.Code,
		Summary:  req.Summary,
		Order:    req.Order,
		Status:   req.Status,
		ParentId: req.ParentId,
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDeptRepository.Create(ctx, dept); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDeptService) Update(ctx context.Context, id uint, req *v1.SysDeptUpdateParams) error {
	dept, err := s.sysDeptRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Name != "" {
		dept.Name = req.Name
	}
	if req.Code != "" {
		dept.Code = req.Code
	}
	if req.Summary != "" {
		dept.Summary = req.Summary
	}
	dept.Order = req.Order
	if req.Status {
		dept.Status = req.Status
	}
	dept.ParentId = req.ParentId

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDeptRepository.Update(ctx, dept); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDeptService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDeptRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDeptService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDeptRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDeptService) Get(ctx context.Context, id uint) (*v1.SysDeptResponse, error) {
	dept, err := s.sysDeptRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return &v1.SysDeptResponse{
		ID:        dept.ID,
		Name:      dept.Name,
		Code:      dept.Code,
		Summary:   dept.Summary,
		Order:     dept.Order,
		Status:    dept.Status,
		ParentId:  dept.ParentId,
		CreatedAt: dept.CreatedAt.Format(time.RFC3339),
		UpdatedAt: dept.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (s *sysDeptService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 编码筛选
	code := ctx.(*gin.Context).DefaultQuery("code", "")
	if code != "" {
		params.AddFilter("code", code)
	}

	depts, total, err := s.sysDeptRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysDeptResponse, 0)
	for _, dept := range depts {
		records = append(records, &v1.SysDeptResponse{
			ID:        dept.ID,
			Name:      dept.Name,
			Code:      dept.Code,
			Summary:   dept.Summary,
			Order:     dept.Order,
			Status:    dept.Status,
			ParentId:  dept.ParentId,
			CreatedAt: dept.CreatedAt.Format(time.RFC3339),
			UpdatedAt: dept.UpdatedAt.Format(time.RFC3339),
		})
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
